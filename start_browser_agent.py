#!/usr/bin/env python3
"""
Startup script for UIUC Browser Agent
Ensures the OpenAI wrapper server is running and starts browser automation
"""

import subprocess
import time
import requests
import sys
import os
from pathlib import Path


def check_server_running(url: str = "http://localhost:8000/health", timeout: int = 5) -> bool:
    """Check if the UIUC OpenAI wrapper server is running"""
    try:
        response = requests.get(url, timeout=timeout)
        return response.status_code == 200
    except:
        return False


def start_server():
    """Start the UIUC OpenAI wrapper server"""
    print("🚀 Starting UIUC OpenAI wrapper server...")
    
    # Check if server file exists
    server_file = Path("uiuc_openai_server.py")
    if not server_file.exists():
        print("❌ Error: uiuc_openai_server.py not found!")
        print("Make sure you're running this from the correct directory.")
        return None
    
    try:
        # Start the server in the background
        process = subprocess.Popen([
            sys.executable, "uiuc_openai_server.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a bit for server to start
        print("⏳ Waiting for server to start...")
        time.sleep(5)
        
        # Check if server is running
        if check_server_running():
            print("✅ Server started successfully!")
            return process
        else:
            print("❌ Server failed to start properly")
            return None
            
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return None


def main():
    """Main startup function"""
    print("🌐 UIUC Browser Agent Startup")
    print("=" * 40)
    
    # Check if server is already running
    if check_server_running():
        print("✅ UIUC OpenAI wrapper server is already running")
    else:
        print("🔍 Server not running, starting it...")
        server_process = start_server()
        
        if not server_process:
            print("❌ Failed to start server. Please start it manually:")
            print("   python uiuc_openai_server.py")
            return
    
    print("\n🎯 Server is ready! You can now:")
    print("1. Run browser examples: python browser_examples.py")
    print("2. Use the browser agent directly: python uiuc_browser_agent.py")
    print("3. Import and use in your own scripts")
    
    print("\n📖 Example usage:")
    print("""
from uiuc_browser_agent import UIUCBrowserAgent
import asyncio

async def my_task():
    agent = UIUCBrowserAgent()
    result = await agent.run_task("Go to google.com and search for 'AI'")
    print(result)

asyncio.run(my_task())
    """)
    
    # Ask if user wants to run examples
    print("\n" + "=" * 40)
    choice = input("Would you like to run the browser examples now? (y/n): ").strip().lower()
    
    if choice in ['y', 'yes']:
        print("\n🚀 Starting browser examples...")
        try:
            # Import and run examples
            import browser_examples
            browser_examples.main()
        except ImportError:
            print("❌ Error importing browser_examples.py")
        except Exception as e:
            print(f"❌ Error running examples: {e}")
    else:
        print("\n👋 Setup complete! You can run examples anytime with:")
        print("   python browser_examples.py")


if __name__ == "__main__":
    main()
