#!/usr/bin/env python3
"""
Browser Automation Examples using UIUC Qwen Model
Demonstrates various browser automation tasks with the UIUC hosted model
"""

import asyncio
import time
from uiuc_browser_agent import UIUCBrowserAgent


class BrowserExamples:
    """Collection of browser automation examples"""
    
    def __init__(self, headless: bool = False, server_url: str = "http://localhost:8000/v1"):
        """
        Initialize browser examples
        
        Args:
            headless: Whether to run browser in headless mode
            server_url: URL of the UIUC OpenAI wrapper server
        """
        self.agent = UIUCBrowserAgent(
            server_url=server_url,
            headless=headless
        )
    
    async def search_example(self):
        """Example: Search on Google"""
        print("\n🔍 Example 1: Google Search")
        print("-" * 40)
        
        task = "Go to google.com and search for 'UIUC computer science department'. Click on the first result."
        
        try:
            result = await self.agent.run_task(task)
            print("✅ Search completed successfully!")
            return result
        except Exception as e:
            print(f"❌ Search failed: {e}")
            return None
    
    async def form_filling_example(self):
        """Example: Fill out a form"""
        print("\n📝 Example 2: Form Filling")
        print("-" * 40)
        
        task = """
        Go to https://httpbin.org/forms/post and fill out the form with the following information:
        - Customer name: <PERSON> Doe
        - Telephone: ************
        - Email: <EMAIL>
        - Size: Medium
        - Topping: cheese
        - Delivery time: now
        Then submit the form.
        """
        
        try:
            result = await self.agent.run_task(task)
            print("✅ Form filling completed successfully!")
            return result
        except Exception as e:
            print(f"❌ Form filling failed: {e}")
            return None
    
    async def data_extraction_example(self):
        """Example: Extract data from a website"""
        print("\n📊 Example 3: Data Extraction")
        print("-" * 40)
        
        task = """
        Go to https://quotes.toscrape.com/ and extract the first 5 quotes.
        For each quote, get the text, author, and tags.
        Present the information in a clear format.
        """
        
        try:
            result = await self.agent.run_task(task)
            print("✅ Data extraction completed successfully!")
            return result
        except Exception as e:
            print(f"❌ Data extraction failed: {e}")
            return None
    
    async def navigation_example(self):
        """Example: Complex navigation"""
        print("\n🧭 Example 4: Complex Navigation")
        print("-" * 40)
        
        task = """
        Go to https://news.ycombinator.com/ (Hacker News).
        Find the top 3 stories and for each story:
        1. Get the title
        2. Get the number of points
        3. Get the number of comments
        Present this information in a summary.
        """
        
        try:
            result = await self.agent.run_task(task)
            print("✅ Navigation completed successfully!")
            return result
        except Exception as e:
            print(f"❌ Navigation failed: {e}")
            return None
    
    async def shopping_example(self):
        """Example: E-commerce interaction"""
        print("\n🛒 Example 5: Shopping Simulation")
        print("-" * 40)
        
        task = """
        Go to https://demo.opencart.com/ and:
        1. Search for 'laptop'
        2. Look at the first laptop product
        3. Get the product name and price
        4. Add it to the cart (but don't complete the purchase)
        5. Report what you found and did
        """
        
        try:
            result = await self.agent.run_task(task)
            print("✅ Shopping simulation completed successfully!")
            return result
        except Exception as e:
            print(f"❌ Shopping simulation failed: {e}")
            return None
    
    async def custom_task(self, task_description: str):
        """Run a custom task"""
        print(f"\n🎯 Custom Task: {task_description}")
        print("-" * 40)
        
        try:
            result = await self.agent.run_task(task_description)
            print("✅ Custom task completed successfully!")
            return result
        except Exception as e:
            print(f"❌ Custom task failed: {e}")
            return None


async def run_all_examples():
    """Run all browser automation examples"""
    print("🌐 UIUC Browser Automation Examples")
    print("=" * 50)
    print("Using UIUC hosted Qwen2.5-VL-72B-Instruct model")
    print("Make sure the UIUC OpenAI wrapper server is running on localhost:8000")
    print("=" * 50)
    
    # Create examples instance
    examples = BrowserExamples(headless=False)  # Set to True for headless mode
    
    # Run examples one by one
    await examples.search_example()
    await asyncio.sleep(2)  # Brief pause between examples
    
    await examples.form_filling_example()
    await asyncio.sleep(2)
    
    await examples.data_extraction_example()
    await asyncio.sleep(2)
    
    await examples.navigation_example()
    await asyncio.sleep(2)
    
    await examples.shopping_example()
    
    print("\n🎉 All examples completed!")


async def run_interactive_mode():
    """Interactive mode for custom tasks"""
    print("🌐 UIUC Browser Agent - Interactive Mode")
    print("=" * 50)
    print("Enter browser automation tasks. Type 'quit' to exit.")
    print("Example: 'Go to wikipedia.org and search for artificial intelligence'")
    print("=" * 50)
    
    examples = BrowserExamples(headless=False)
    
    while True:
        try:
            task = input("\nEnter task: ").strip()
            
            if task.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if not task:
                continue
            
            print(f"\n🤖 Executing: {task}")
            result = await examples.custom_task(task)
            
            if result:
                print(f"\n📋 Result: {result}")
            
        except KeyboardInterrupt:
            print("\n\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


def main():
    """Main function with menu"""
    print("🌐 UIUC Browser Automation")
    print("=" * 30)
    print("1. Run all examples")
    print("2. Interactive mode")
    print("3. Quick test")
    print("=" * 30)
    
    choice = input("Choose an option (1-3): ").strip()
    
    if choice == "1":
        asyncio.run(run_all_examples())
    elif choice == "2":
        asyncio.run(run_interactive_mode())
    elif choice == "3":
        # Quick test
        async def quick_test():
            examples = BrowserExamples(headless=False)
            await examples.search_example()
        
        asyncio.run(quick_test())
    else:
        print("Invalid choice. Please run again and choose 1, 2, or 3.")


if __name__ == "__main__":
    main()
