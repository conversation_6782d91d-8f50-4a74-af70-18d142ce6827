#!/usr/bin/env python3
"""
UIUC Browser Agent - Integrates browser-use with UIUC hosted Qwen model
This creates a browser automation agent using the existing UIUC OpenAI wrapper
"""

import asyncio
import json
import requests
from typing import List, Dict, Any, Optional, Iterator, Union
from browser_use import Agent
from browser_use.llm import ChatOpenAI
import time
import uuid
def create_uiuc_llm(
    base_url: str = "http://localhost:8000/v1",
    api_key: str = "EMPTY",
    model: str = "Qwen/Qwen2.5-VL-72B-Instruct",
    temperature: float = 0.1,
    max_tokens: Optional[int] = 2000,
    **kwargs
) -> ChatOpenAI:
    """Create a ChatOpenAI instance configured for UIUC server"""

    return ChatOpenAI(
        base_url=base_url,
        api_key=api_key,
        model=model,
        temperature=temperature,
        max_completion_tokens=max_tokens,
        **kwargs
    )


class UIUCBrowserAgent:
    """Browser automation agent using UIUC Qwen model"""

    def __init__(
        self,
        server_url: str = "http://localhost:8000/v1",
        api_key: str = "EMPTY",
        model: str = "Qwen/Qwen2.5-VL-72B-Instruct",
        temperature: float = 0.1,
        max_tokens: Optional[int] = 2000,
        headless: bool = False
    ):
        """
        Initialize the browser agent

        Args:
            server_url: URL of the UIUC OpenAI wrapper server
            api_key: API key (usually "EMPTY" for local server)
            model: Model name
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            headless: Whether to run browser in headless mode
        """
        self.llm = create_uiuc_llm(
            base_url=server_url,
            api_key=api_key,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens
        )
        self.headless = headless
    
    async def run_task(self, task: str, **kwargs) -> Any:
        """
        Run a browser automation task
        
        Args:
            task: Description of the task to perform
            **kwargs: Additional arguments for the Agent
            
        Returns:
            Result of the task execution
        """
        
        # Create browser-use agent with our custom LLM
        agent = Agent(
            task=task,
            llm=self.llm,
            headless=self.headless,
            **kwargs
        )
        
        # Run the task
        result = await agent.run()
        return result
    
    def run_task_sync(self, task: str, **kwargs) -> Any:
        """Synchronous version of run_task"""
        return asyncio.run(self.run_task(task, **kwargs))


# Example usage functions
async def test_browser_agent():
    """Test the browser agent with a simple task"""
    
    print("🤖 Testing UIUC Browser Agent")
    print("=" * 50)
    
    # Create the agent
    agent = UIUCBrowserAgent(
        server_url="http://localhost:8000/v1",
        headless=False  # Set to True to run without GUI
    )
    
    # Test task
    task = "Go to google.com and search for 'UIUC computer science'"
    
    print(f"Task: {task}")
    print("Starting browser automation...")
    
    try:
        result = await agent.run_task(task)
        print(f"✅ Task completed successfully!")
        print(f"Result: {result}")
    except Exception as e:
        print(f"❌ Task failed: {e}")


def test_browser_agent_sync():
    """Synchronous test function"""
    asyncio.run(test_browser_agent())


if __name__ == "__main__":
    # Run the test
    test_browser_agent_sync()
