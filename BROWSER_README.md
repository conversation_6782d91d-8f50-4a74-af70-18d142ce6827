# UIUC Browser Agent

Browser automation powered by UIUC hosted Qwen2.5-VL-72B-Instruct model using browser-use framework.

## Overview

This integration allows you to use your existing UIUC chat AI agent to perform browser automation tasks. The agent can:

- Navigate websites
- Fill out forms
- Extract data from web pages
- Perform searches
- Interact with web applications
- Take screenshots and analyze visual content

## Setup

### Prerequisites

1. **Python 3.11+** (browser-use requirement)
2. **UIUC OpenAI wrapper server** running on localhost:8000
3. **Browser dependencies** (Chromium via Playwright)

### Installation

1. **Create conda environment with Python 3.11:**
   ```bash
   conda create -n browser_use_env python=3.11 -y
   conda activate browser_use_env
   ```

2. **Install dependencies:**
   ```bash
   pip install browser-use requests flask
   playwright install chromium
   ```

3. **Start the UIUC OpenAI wrapper server:**
   ```bash
   # In the original openhands_env or your main environment
   python uiuc_openai_server.py
   ```

## Quick Start

### Option 1: Use the startup script
```bash
conda activate browser_use_env
python start_browser_agent.py
```

### Option 2: Run examples directly
```bash
conda activate browser_use_env
python browser_examples.py
```

### Option 3: Use in your own code
```python
from uiuc_browser_agent import UIUCBrowserAgent
import asyncio

async def my_browser_task():
    agent = UIUCBrowserAgent(headless=False)  # Set True for headless mode
    
    task = "Go to google.com and search for 'UIUC computer science'"
    result = await agent.run_task(task)
    print(result)

# Run the task
asyncio.run(my_browser_task())
```

## Examples

The `browser_examples.py` file includes several demonstration tasks:

1. **Google Search** - Navigate and search
2. **Form Filling** - Fill out web forms
3. **Data Extraction** - Scrape information from websites
4. **Complex Navigation** - Multi-step website interactions
5. **Shopping Simulation** - E-commerce interactions

### Running Examples

```bash
conda activate browser_use_env
python browser_examples.py
```

Choose from:
- Run all examples
- Interactive mode (enter custom tasks)
- Quick test

## Configuration

### Server Settings

The browser agent connects to your UIUC OpenAI wrapper server:

```python
agent = UIUCBrowserAgent(
    server_url="http://localhost:8000/v1",  # Your server URL
    api_key="EMPTY",                        # Usually "EMPTY" for local
    model="Qwen/Qwen2.5-VL-72B-Instruct",  # Model name
    temperature=0.1,                        # Sampling temperature
    max_tokens=2000,                        # Max response length
    headless=False                          # Browser visibility
)
```

### Browser Settings

- **Headless mode**: Set `headless=True` to run without GUI
- **Browser type**: Currently uses Chromium (can be configured)
- **Viewport**: Default browser window size
- **Screenshots**: Automatically taken for visual analysis

## Task Examples

### Simple Tasks
```python
# Basic navigation
"Go to google.com and search for 'artificial intelligence'"

# Form interaction
"Fill out the contact form with name 'John Doe' and email '<EMAIL>'"

# Data extraction
"Go to news.ycombinator.com and get the titles of the top 5 stories"
```

### Complex Tasks
```python
# Multi-step workflow
"""
Go to amazon.com, search for 'laptop', 
filter by price under $1000, 
and get the names and prices of the first 3 results
"""

# E-commerce simulation
"""
Go to an online store, add a product to cart, 
proceed to checkout (but don't complete purchase), 
and report the total price
"""
```

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Your Task     │───▶│  UIUC Browser    │───▶│   browser-use   │
│   Description   │    │     Agent        │    │   Framework     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │ UIUC OpenAI      │    │   Playwright    │
                       │ Wrapper Server   │    │    Browser      │
                       └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │ UIUC Qwen Model  │    │   Web Browser   │
                       │ (Qwen2.5-VL-72B) │    │   (Chromium)    │
                       └──────────────────┘    └─────────────────┘
```

## Troubleshooting

### Common Issues

1. **Server not running**
   ```
   Error: Connection refused to localhost:8000
   ```
   **Solution**: Start the UIUC OpenAI wrapper server first:
   ```bash
   python uiuc_openai_server.py
   ```

2. **Python version error**
   ```
   ERROR: Requires-Python >=3.11
   ```
   **Solution**: Use the browser_use_env environment with Python 3.11

3. **Browser not found**
   ```
   Error: Chromium not found
   ```
   **Solution**: Install Playwright browsers:
   ```bash
   playwright install chromium
   ```

4. **Permission errors**
   ```
   Error: Failed to install browsers
   ```
   **Solution**: Install without system dependencies:
   ```bash
   playwright install chromium
   ```

### Debug Mode

Enable verbose logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)

agent = UIUCBrowserAgent(headless=False)
```

## Advanced Usage

### Custom Functions

You can extend the browser agent with custom functions:

```python
from browser_use import Agent
from uiuc_browser_agent import UIUCChatLLM

# Create custom LLM
llm = UIUCChatLLM()

# Define custom functions
def save_to_file(content: str, filename: str):
    """Save content to a file"""
    with open(filename, 'w') as f:
        f.write(content)
    return f"Saved to {filename}"

# Create agent with custom functions
agent = Agent(
    task="Extract data and save to file",
    llm=llm,
    additional_functions=[save_to_file]
)
```

### Batch Processing

Process multiple tasks:
```python
async def batch_process(tasks):
    agent = UIUCBrowserAgent()
    results = []
    
    for task in tasks:
        result = await agent.run_task(task)
        results.append(result)
    
    return results

tasks = [
    "Search for 'AI news' on Google",
    "Go to Wikipedia and search for 'machine learning'",
    "Visit news.ycombinator.com and get top story"
]

results = asyncio.run(batch_process(tasks))
```

## Support

- **Discord**: Join the browser-use community
- **GitHub**: Report issues on the browser-use repository
- **UIUC Chat**: Use your existing UIUC model access

## License

This integration follows the same license as browser-use (MIT) and respects UIUC's usage policies.
